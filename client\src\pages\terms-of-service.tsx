import { Link, useLocation } from "wouter";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getTranslation } from "@/lib/i18n";

export default function TermsOfService() {
  const [location] = useLocation();
  const searchParams = new URLSearchParams(location.split('?')[1] || '');
  const language = searchParams.get('lang') || 'en';

  return (
    <div className="bg-slate-50 font-sans min-h-screen">
      <header className="bg-white border-b border-slate-200">
        <nav className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                {getTranslation(language, 'backToApp')}
              </Button>
            </Link>
          </div>
        </nav>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-8">
          <h1 className="text-3xl font-bold text-slate-800 mb-8">{getTranslation(language, 'termsOfService')}</h1>

          <div className="prose prose-slate max-w-none">
            <p className="text-slate-600 mb-6">
              {getTranslation(language, 'lastUpdated')} {new Date().toLocaleDateString()}
            </p>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'acceptanceOfTerms')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'acceptanceContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'useLicense')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'useLicenseContent')}
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>{getTranslation(language, 'useLicenseList1')}</li>
                <li>{getTranslation(language, 'useLicenseList2')}</li>
                <li>{getTranslation(language, 'useLicenseList3')}</li>
                <li>{getTranslation(language, 'useLicenseList4')}</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'serviceAvailability')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'serviceAvailabilityContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'userConduct')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'userConductContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'privacy')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'privacyContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'limitations')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'limitationsContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'modifications')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'modificationsContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'contactInformation')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'contactInformationContent')}
              </p>
            </section>
          </div>
        </div>
      </main>
    </div>
  );
}